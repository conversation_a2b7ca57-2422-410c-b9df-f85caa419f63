/**
 * Custom hook for managing session statistics
 */

import { useState, useEffect, useCallback } from 'react';

interface SessionStats {
  documentsUploaded: number;
  analysesCompleted: number;
  tokensRemaining: string;
  processingStatus: string;
  sessionDuration: string;
}

export const useSessionStats = () => {
  const [stats, setStats] = useState<SessionStats>({
    documentsUploaded: 0,
    analysesCompleted: 0,
    tokensRemaining: '2,500',
    processingStatus: 'Ready',
    sessionDuration: '0m'
  });

  const [sessionStartTime] = useState(Date.now());

  const updateStats = useCallback(() => {
    // Get documents from sessionStorage
    const storedFiles = JSON.parse(sessionStorage.getItem('casebuilder-files') || '[]');
    const documentsUploaded = storedFiles.length;

    // Get analyses from sessionStorage
    const storedAnalyses = JSON.parse(sessionStorage.getItem('casebuilder-analyses') || '[]');
    const analysesCompleted = storedAnalyses.length;

    // Calculate session duration
    const currentTime = Date.now();
    const durationMs = currentTime - sessionStartTime;
    const durationMinutes = Math.floor(durationMs / (1000 * 60));
    const durationHours = Math.floor(durationMinutes / 60);
    
    let sessionDuration: string;
    if (durationHours > 0) {
      sessionDuration = `${durationHours}h ${durationMinutes % 60}m`;
    } else {
      sessionDuration = `${durationMinutes}m`;
    }

    // Calculate tokens remaining (mock calculation based on analyses)
    const baseTokens = 2500;
    const tokensUsed = analysesCompleted * 100; // Assume 100 tokens per analysis
    const tokensRemaining = Math.max(0, baseTokens - tokensUsed);

    setStats({
      documentsUploaded,
      analysesCompleted,
      tokensRemaining: tokensRemaining.toLocaleString(),
      processingStatus: 'Ready',
      sessionDuration
    });
  }, [sessionStartTime]);

  useEffect(() => {
    // Initial update
    updateStats();

    // Listen for file changes
    const handleFileChange = () => {
      updateStats();
    };

    // Listen for analysis completion
    const handleAnalysisChange = () => {
      updateStats();
    };

    // Add event listeners
    window.addEventListener('casebuilder-files-changed', handleFileChange);
    window.addEventListener('casebuilder-analysis-completed', handleAnalysisChange);

    // Update every minute for session duration
    const interval = setInterval(updateStats, 60000);

    return () => {
      window.removeEventListener('casebuilder-files-changed', handleFileChange);
      window.removeEventListener('casebuilder-analysis-completed', handleAnalysisChange);
      clearInterval(interval);
    };
  }, [sessionStartTime, updateStats]);

  return stats;
};
